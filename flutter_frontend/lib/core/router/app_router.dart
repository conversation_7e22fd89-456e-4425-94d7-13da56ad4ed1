// lib/core/router/app_router.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';

// Import auth bloc and states
import '../../features/auth/blocs/auth_bloc.dart';
import '../../features/auth/utils/callback_handler.dart' as auth_callback; // Added for direct callback handling with prefix
// Import ACTUAL auth screens
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/signup_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart'; // Import wrapper
// import '../../features/auth/screens/temp_forgot_password_screen.dart'; // Removed temp screen import
import '../../features/auth/screens/confirmation_success_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart'; // New screen
import '../../features/auth/screens/check_email_screen.dart'; // Import actual screen
import '../../features/auth/screens/callback_screen.dart'; // Import CallbackScreen
import '../../features/auth/screens/club_registration_screen.dart'; // Import ClubRegistrationScreen

// Import placeholder screens (for non-auth screens or general placeholders)
import '../../presentation/widgets/placeholder_screens.dart' as placeholder; // Keep for other placeholders if used
// Import the actual HomeScreen
import '../../features/home/<USER>/home_screen.dart'; // Import actual HomeScreen
import '../../features/home/<USER>/director_dashboard_screen.dart'; // Import DirectorDashboardScreen
import '../../features/profile/screens/profile_screen.dart'; // Import actual ProfileScreen
import '../../features/profile/screens/edit_profile_details_screen.dart'; // Import EditProfileDetailsScreen
import '../../features/profile/screens/change_password_screen.dart'; // Import ChangePasswordScreen
import '../../data/models/user_profile.dart'; // Import UserProfile

// Import tournament creation screens & BLoC components
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_1_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/division_matrix_setup_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_2_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_2_venues_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_3_fields_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_additional_info_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_venue_field_selection_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/game_timing_configuration_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/venue_form_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/field_form_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_review_screen.dart';
import 'package:soccer_frontend/data/models/venue.dart' as app_venue_model; // Alias for Venue model
import 'package:soccer_frontend/data/models/field.dart'; // Import Field model

// Import scheduling screen and BLoC components
import 'package:soccer_frontend/features/scheduling/screens/field_schedule_screen.dart';
import 'package:soccer_frontend/features/scheduling/blocs/field_schedule_bloc.dart';
// import 'package:soccer_frontend/features/scheduling/blocs/field_schedule_event.dart'; // Not directly used here
import 'package:soccer_frontend/features/scheduling/services/scheduling_api_service.dart';
import 'package:soccer_frontend/features/tournament/screens/my_tournaments_screen.dart'; // Added import
import 'package:soccer_frontend/features/tournament/screens/tournament_detail_screen.dart'; // Added import
import 'package:soccer_frontend/features/tournament/screens/create_tournament/registration_preview_screen.dart'; // Added import
import 'package:soccer_frontend/data/models/tournament.dart'; // Added import


/// Class responsible for creating and configuring the app's router
class AppRouter {
  final AuthBloc authBloc;
  final auth_callback.CallbackHandler callbackHandler; // Added & Prefixed
  final _log = Logger('AppRouter');

  AppRouter({required this.authBloc, required this.callbackHandler}) { // Modified constructor
    _log.info('AppRouter initialized');
  }

  /// The GoRouter instance for the app
  late final GoRouter router = _createRouter();

  /// Create the router with all routes and redirect logic
  GoRouter _createRouter() {
    return GoRouter(
      // Listen to Bloc stream for state changes
      refreshListenable: GoRouterRefreshStream(authBloc.stream),

      initialLocation: '/splash', // Start with a splash/loading screen
      debugLogDiagnostics: true, // Keep this enabled for debugging

      routes: [
        // Splash screen (initial route while auth state is checked)
        GoRoute(
          path: '/splash',
          name: 'splash',
          builder: (context, state) => const placeholder.SplashScreen(),
        ),
        // Auth Routes
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(), // Restore LoginScreen
        ),
        GoRoute(
          path: '/signup',
          name: 'signup',
          builder: (context, state) => const SignupScreen(), // Uses imported actual SignupScreen
        ),
        GoRoute(
          path: '/register-club',
          name: 'registerClub',
          builder: (context, state) => const ClubRegistrationScreen(),
        ),
        GoRoute( // Add the forgot password route if missing, or modify existing one
          path: '/forgot-password',
          name: 'forgotPassword',
          builder: (context, state) {
            // This print statement is ALREADY in ForgotPasswordScreen.dart's build method.
            // _log.info('>>> AppRouter: Building ForgotPasswordScreenWrapper for /forgot-password <<<');
            return const ForgotPasswordScreenWrapper();
          },
        ),
        // GoRoute( // Removed temporary route
        //   path: '/temp-forgot-password',
        //   name: 'tempForgotPassword',
        //   builder: (context, state) {
        //     _log.info('>>> AppRouter: Building TempForgotPasswordScreen for /temp-forgot-password <<<');
        //     return const TempForgotPasswordScreen();
        //   },
        // ),
        GoRoute(
          path: '/auth/callback', // Add the callback route
          name: 'callback',
          builder: (context, state) {
            _log.info('>>> AppRouter: Building CallbackScreen for /auth/callback <<<');
            // CallbackScreen should handle the session from URL via Supabase SDK internally
            return const CallbackScreen();
          },
        ),
        GoRoute(
          path: '/auth/confirmation-success',
          name: 'confirmationSuccess',
          builder: (context, state) => const ConfirmationSuccessScreen(), // Uses imported actual ConfirmationSuccessScreen
        ),
        GoRoute(
          path: '/auth/reset-password',
          name: 'resetPassword',
          builder: (context, state) => const ResetPasswordScreen(), // New screen
        ),
        // Add check email route if not present
        GoRoute(
          path: '/check-email',
          name: 'checkEmail',
          builder: (context, state) => CheckEmailScreen( // Use actual screen
            email: state.uri.queryParameters['email'],
            // Use the correct parameter name from CheckEmailScreen
            isPasswordReset: (state.uri.queryParameters['reset'] ?? 'false').toLowerCase() == 'true',
          ),
        ),


        // Logged In Routes
        GoRoute(
          path: '/',
          name: 'home',
          builder: (context, state) => const HomeScreen(), // Use actual HomeScreen
        ),
        GoRoute(
          path: '/profile',
          name: 'profile',
          builder: (context, state) => const ProfileScreen(), // Use actual ProfileScreen
        ),
        GoRoute(
          path: '/tournaments',
          name: 'tournaments',
          builder: (context, state) => const placeholder.TournamentsScreen(), // This is a generic public listing
        ),
        GoRoute(
          path: MyTournamentsScreen.routeName, // Using static routeName
          name: 'myTournaments',
          builder: (context, state) => const MyTournamentsScreen(),
        ),
        GoRoute(
          path: '/tournament/:tournamentId', // Path with parameter
          name: 'tournamentDetail',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId'];
            if (tournamentId == null) {
              // Handle error or redirect if ID is missing
              return placeholder.NotFoundScreen(error: 'Tournament ID is missing');
            }
            return TournamentDetailScreen(tournamentId: tournamentId);
          },
        ),
        GoRoute(
          path: '/tournament/:tournamentId/preview-registration',
          name: 'tournamentRegistrationPreview',
          builder: (context, state) {
            final tournament = state.extra as Tournament?;
            if (tournament == null) {
              return placeholder.NotFoundScreen(error: 'Tournament data not provided for preview.');
            }
            return RegistrationPreviewScreen(tournament: tournament);
          },
        ),
        GoRoute(
          path: DirectorDashboardScreen.routeName, // Using static routeName
          name: 'directorDashboard',
          builder: (context, state) => const DirectorDashboardScreen(),
        ),
        GoRoute(
          path: EditProfileDetailsScreen.routeName,
          name: 'editProfileDetails',
          builder: (context, state) {
            // Assuming UserProfile is passed as an extra parameter during navigation
            final userProfile = state.extra as UserProfile?;
            if (userProfile != null) {
              return EditProfileDetailsScreen(userProfile: userProfile);
            }
            // Handle missing profile, e.g., redirect or show error
            return placeholder.NotFoundScreen(error: 'User profile not provided for edit screen.');
          },
        ),
        GoRoute(
          path: ChangePasswordScreen.routeName,
          name: 'changePassword',
          builder: (context, state) => const ChangePasswordScreen(),
        ),
        // Tournament Creation Routes
        ShellRoute(
          builder: (context, state, child) {
            return BlocProvider<CreateTournamentBloc>(
              create: (context) {
                final apiService = RepositoryProvider.of<TournamentApiService>(context);
                return CreateTournamentBloc(tournamentApiService: apiService)
                  ..add(InitializeTournamentCreation());
              },
              child: child,
            );
          },
          routes: [
            GoRoute(
              path: '/create-tournament/step1',
              name: 'createTournamentStep1',
              builder: (context, state) => const CreateTournamentStep1Screen(),
            ),
            GoRoute(
              path: '/create-tournament/division-matrix',
              name: 'createTournamentDivisionMatrix',
              builder: (context, state) => const DivisionMatrixSetupScreen(),
            ),
            GoRoute(
              path: '/create-tournament/venue-field-selection',
              name: 'createTournamentVenueFieldSelection',
              builder: (context, state) => const CreateTournamentVenueFieldSelectionScreen(),
            ),
            GoRoute(
              path: '/create-tournament/game-timing',
              name: 'createTournamentGameTiming',
              builder: (context, state) => const GameTimingConfigurationScreen(),
            ),
            GoRoute(
              path: '/create-tournament/additional-info',
              name: 'createTournamentAdditionalInfo',
              builder: (context, state) => const CreateTournamentAdditionalInfoScreen(),
            ),
            GoRoute(
              path: '/create-tournament/step2',
              name: 'createTournamentStep2',
              builder: (context, state) => const CreateTournamentStep2Screen(),
            ),
            GoRoute(
              path: '/create-tournament/step3',
              name: 'createTournamentStep3',
              builder: (context, state) => const CreateTournamentStep2VenuesScreen(),
            ),
            GoRoute(
              path: '/create-tournament/step4',
              name: 'createTournamentStep4',
              builder: (context, state) => const CreateTournamentStep3FieldsScreen(),
            ),
            GoRoute(
              path: '/create-tournament/venue-form',
              name: 'venueForm',
              builder: (context, state) {
                final app_venue_model.Venue? venue = state.extra as app_venue_model.Venue?;
                return VenueFormScreen(venue: venue);
              },
            ),
            GoRoute(
              path: '/create-tournament/venues/:venueId/fields/add',
              name: 'addFieldInTournament',
              builder: (context, state) {
                final venueId = state.pathParameters['venueId']!;
                final Field? field = state.extra as Field?;
                return FieldFormScreen(venueId: venueId, field: field);
              },
            ),
            GoRoute(
              path: '/create-tournament/review',
              name: 'createTournamentReview',
              builder: (context, state) => const CreateTournamentReviewScreen(),
            ),
          ],
        ),
        // Venue Management Routes
        GoRoute(
          path: '/venues/add',
          name: 'addVenue',
          builder: (context, state) {
            return VenueFormScreen(venue: null);
          },
        ),
        // Field Management Routes
        GoRoute(
          path: '/venues/:venueId/fields/add',
          name: 'addField',
          builder: (context, state) {
            final venueId = state.pathParameters['venueId']!;
            return FieldFormScreen(venueId: venueId, field: null);
          },
        ),
        // Field Schedule Route
        GoRoute(
          path: '/tournaments/:tournamentId/fields/:fieldId/schedule',
          name: 'fieldSchedule',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId']!;
            final fieldId = state.pathParameters['fieldId']!;
            final schedulingApiService = RepositoryProvider.of<SchedulingApiService>(context);
            return BlocProvider<FieldScheduleBloc>(
              create: (context) => FieldScheduleBloc(schedulingApiService: schedulingApiService),
              child: FieldScheduleScreen(tournamentId: tournamentId, fieldId: fieldId),
            );
          },
        ),
      ],
      errorBuilder: (context, state) => placeholder.NotFoundScreen(error: state.error?.toString()),

      redirect: (BuildContext context, GoRouterState state) {
        final authState = authBloc.state; // Using instance variable from AppRouter class
        final location = state.matchedLocation;
        final fullUri = state.uri; // Use full URI for detailed logging

        _log.fine('[Router] Redirect Check: MatchedLocation="$location", FullURI="$fullUri", AuthState=${authState.runtimeType}');

        // Handle splash screen explicitly first
        if (location == '/splash') {
          if (authState is AuthInitial || authState is AuthLoading) {
            _log.fine('[Router] Staying on /splash (AuthInitial or AuthLoading)');
            return null; // Stay on splash
          } else {
            // Once auth state is determined (not initial/loading), navigate away from splash
            String destination = '/login'; // Default for unauthenticated
            if (authState is AuthAuthenticated) {
              if (authState.userProfile?.role == 'tournament_director') { // Corrected to lowercase
                destination = DirectorDashboardScreen.routeName;
              } else {
                destination = '/'; // Default home for other authenticated users
              }
            } else if (authState is AuthAuthenticatedNoProfile) {
              // This case might need specific handling, e.g. redirect to profile setup
              // For now, treating like other authenticated users or a generic home.
              destination = '/';
            }
            _log.info('[Router] Leaving /splash, redirecting to $destination based on role: ${authState is AuthAuthenticated ? authState.userProfile?.role : 'N/A'}');
            return destination;
          }
        }

        final isPublicRoute = [
          '/login',
          '/signup',
          '/register-club', // Add to public routes
          '/forgot-password',
          // '/temp-forgot-password', // Removed from public routes
          '/auth/reset-password',
          '/auth/callback',
          '/auth/confirmation-success', // Added to public routes
          '/auth/invalid-link',
          '/check-email',
          '/splash',
        ].contains(location);

        final isAuthenticated = authState is AuthAuthenticated;
        final isRecovering = authState is AuthPasswordRecovery;

        // Handle password recovery flow first
        if (isRecovering) {
          _log.fine('[Router] AuthPasswordRecovery state. Current location: $location');
          if (location != '/auth/reset-password') {
            _log.info('[Router] Redirecting to /auth/reset-password due to AuthPasswordRecovery state.');
            return '/auth/reset-password';
          }
          _log.fine('[Router] Already on /auth/reset-password during AuthPasswordRecovery. No redirect.');
          return null;
        }

        // If authenticated
        if (isAuthenticated) {
          _log.fine('[Router] Authenticated user. Current location: $location. Role: ${authState.userProfile?.role}');
          final String targetDashboard = authState.userProfile?.role == 'tournament_director'
              ? DirectorDashboardScreen.routeName
              : '/';

          // If authenticated user is on login, signup, or a page they shouldn't be on post-auth
          if (location == '/login' || location == '/signup' || location == '/auth/confirmation-success' || location == '/check-email') {
            _log.info('[Router] Authenticated user on $location. Redirecting to $targetDashboard.');
            return targetDashboard;
          }
          // If authenticated user is on generic home ('/') but should be on director dashboard
          if (location == '/' && targetDashboard == DirectorDashboardScreen.routeName) {
            _log.info('[Router] Authenticated Tournament Director on "/". Redirecting to Director Dashboard.');
            return DirectorDashboardScreen.routeName;
          }
          // Otherwise, allow access to the current protected route they are on (e.g., /profile, /director-dashboard already)
          _log.fine('[Router] Authenticated user on $location. Access allowed.');
          return null;
        }

        // If NOT authenticated
        if (!isPublicRoute) {
          _log.info('[Router] Unauthenticated user on protected route $location. Redirecting to /login.');
          return '/login';
        }

        // Unauthenticated user on a public route, allow access
        _log.fine('[Router] Unauthenticated user on public route $location. No redirect.');
        return null;
      },
    );
  }
}

/// Helper class to bridge Bloc stream to Listenable for GoRouter
class GoRouterRefreshStream extends ChangeNotifier {
  late final StreamSubscription<dynamic> _subscription;
  final _log = Logger('GoRouterRefreshStream');

  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners(); // Initial notify
    _subscription = stream.asBroadcastStream().listen(
      (dynamic _) {
        _log.fine('Received stream event, notifying GoRouter.');
        notifyListeners(); // Notify GoRouter on every stream event
      },
      onError: (error) {
        _log.warning('Error in refresh stream: $error');
        notifyListeners(); // Still notify on error
      }
    );
  }

  @override
  void dispose() {
    _subscription.cancel();
    _log.info('Refresh stream subscription cancelled.');
    super.dispose();
  }
}
