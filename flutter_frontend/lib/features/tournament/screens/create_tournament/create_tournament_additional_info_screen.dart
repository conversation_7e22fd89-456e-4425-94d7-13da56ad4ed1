// soccer_frontend/features/tournament/presentation/screens/create_tournament_additional_info_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:intl/intl.dart'; // For date formatting

class CreateTournamentAdditionalInfoScreen extends StatefulWidget {
  const CreateTournamentAdditionalInfoScreen({super.key});

  @override
  State<CreateTournamentAdditionalInfoScreen> createState() =>
      _CreateTournamentAdditionalInfoScreenState();
}

class _CreateTournamentAdditionalInfoScreenState
    extends State<CreateTournamentAdditionalInfoScreen> {
  final _formKey = GlobalKey<FormState>();

  // Social media controllers
  late TextEditingController _facebookUrlController;
  late TextEditingController _twitterUrlController;
  late TextEditingController _instagramUrlController;
  late TextEditingController _websiteUrlController;

  // Tournament details controllers
  late TextEditingController _descriptionController;

  // Registration details controllers
  late TextEditingController _maxTeamsController;

  // Tournament structure controllers
  // REMOVED: late TextEditingController _gameDurationController; // Global game duration is removed
  late TextEditingController _minRosterSizeController;
  late TextEditingController _maxRosterSizeController;

  // Awards and amenities controllers
  late TextEditingController _awardsController;
  bool _hasConcessions = false;
  bool _hasMerchandise = false;
  bool _hasMedical = false;

  // Spectator information controllers
  late TextEditingController _admissionFeeController;
  late TextEditingController _parkingInfoController;
  late TextEditingController _spectatorInfoController;

  // Additional contacts controllers
  late TextEditingController _secondaryContactNameController;
  late TextEditingController _secondaryContactEmailController;
  late TextEditingController _secondaryContactPhoneController;
  late TextEditingController _secondaryContactRoleController;

  // Logo upload state
  bool _isUploadingLogo = false;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _facebookUrlController = TextEditingController();
    _twitterUrlController = TextEditingController();
    _instagramUrlController = TextEditingController();
    _websiteUrlController = TextEditingController();

    _descriptionController = TextEditingController();

    _maxTeamsController = TextEditingController();

    _minRosterSizeController = TextEditingController();
    _maxRosterSizeController = TextEditingController();

    _awardsController = TextEditingController();

    _admissionFeeController = TextEditingController();
    _parkingInfoController = TextEditingController();
    _spectatorInfoController = TextEditingController();

    _secondaryContactNameController = TextEditingController();
    _secondaryContactEmailController = TextEditingController();
    _secondaryContactPhoneController = TextEditingController();
    _secondaryContactRoleController = TextEditingController();

    // Pre-populate fields from the tournament if available
    final blocState = context.read<CreateTournamentBloc>().state;
    if (blocState is CreateTournamentAdditionalInfoStep) {
      final tournament = blocState.tournament;

      // Social media
      if (tournament.facebookUrl != null) {
        _facebookUrlController.text = tournament.facebookUrl!;
      }
      if (tournament.twitterUrl != null) {
        _twitterUrlController.text = tournament.twitterUrl!;
      }
      if (tournament.instagramUrl != null) {
        _instagramUrlController.text = tournament.instagramUrl!;
      }
      if (tournament.websiteUrl != null) {
        _websiteUrlController.text = tournament.websiteUrl!;
      }

      // Tournament details
      if (tournament.description != null) {
        _descriptionController.text = tournament.description!;
      }

      // Registration details
      if (tournament.maxTeams != null) {
        _maxTeamsController.text = tournament.maxTeams!.toString();
      }

      // Tournament structure (game duration field is removed)
      if (tournament.minRosterSize != null) {
        _minRosterSizeController.text = tournament.minRosterSize!.toString();
      }
      if (tournament.maxRosterSize != null) {
        _maxRosterSizeController.text = tournament.maxRosterSize!.toString();
      }

      // Awards and amenities
      if (tournament.awards != null) {
        _awardsController.text = tournament.awards!;
      }
      _hasConcessions = tournament.hasConcessions ?? false;
      _hasMerchandise = tournament.hasMerchandise ?? false;
      _hasMedical = tournament.hasMedical ?? false;

      // Spectator information
      if (tournament.admissionFee != null) {
        _admissionFeeController.text = tournament.admissionFee!;
      }
      if (tournament.parkingInfo != null) {
        _parkingInfoController.text = tournament.parkingInfo!;
      }
      if (tournament.spectatorInfo != null) {
        _spectatorInfoController.text = tournament.spectatorInfo!;
      }

      // Additional contacts
      if (tournament.secondaryContactName != null) {
        _secondaryContactNameController.text = tournament.secondaryContactName!;
      }
      if (tournament.secondaryContactEmail != null) {
        _secondaryContactEmailController.text = tournament.secondaryContactEmail!;
      }
      if (tournament.secondaryContactPhone != null) {
        _secondaryContactPhoneController.text = tournament.secondaryContactPhone!;
      }
      if (tournament.secondaryContactRole != null) {
        _secondaryContactRoleController.text = tournament.secondaryContactRole!;
      }
    }
  }

  @override
  void dispose() {
    // Dispose controllers
    _facebookUrlController.dispose();
    _twitterUrlController.dispose();
    _instagramUrlController.dispose();
    _websiteUrlController.dispose();

    _descriptionController.dispose();

    _maxTeamsController.dispose();

    _minRosterSizeController.dispose();
    _maxRosterSizeController.dispose();

    _awardsController.dispose();

    _admissionFeeController.dispose();
    _parkingInfoController.dispose();
    _spectatorInfoController.dispose();

    _secondaryContactNameController.dispose();
    _secondaryContactEmailController.dispose();
    _secondaryContactPhoneController.dispose();
    _secondaryContactRoleController.dispose();

    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    // This listener should only react to successful transitions FROM this screen.
    // The navigation TO this screen is handled by the Bloc's state transitions.
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listenWhen: (previous, current) {
        // Only listen when transitioning from this step to the review step
        return previous is CreateTournamentAdditionalInfoStep && current is CreateTournamentReviewStep;
      },
      listener: (context, state) {
        if (state is CreateTournamentReviewStep) {
          context.go('/create-tournament/review');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Additional Tournament Information'),
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {
                context.read<AuthBloc>().add(AuthSignOutRequested());
              },
              tooltip: 'Sign Out',
            ),
          ],
        ),
        body: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
          builder: (context, state) {
            if (state is! CreateTournamentAdditionalInfoStep) {
              return const Center(child: CircularProgressIndicator());
            }

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // Social Media Section
                      _buildSectionHeader(context, 'Social Media Links'),
                      _buildSocialMediaSection(),
                      const SizedBox(height: 24.0),

                      // Tournament Details Section
                      _buildSectionHeader(context, 'Tournament Details'),
                      _buildTournamentDetailsSection(state),
                      const SizedBox(height: 24.0),

                      // Registration Details Section
                      _buildSectionHeader(context, 'Registration Details'),
                      _buildRegistrationDetailsSection(),
                      const SizedBox(height: 24.0),

                      // Tournament Structure Section (Game Duration removed)
                      _buildSectionHeader(context, 'Tournament Structure'),
                      _buildTournamentStructureSection(),
                      const SizedBox(height: 24.0),

                      // Awards and Amenities Section
                      _buildSectionHeader(context, 'Awards and Amenities'),
                      _buildAwardsAndAmenitiesSection(),
                      const SizedBox(height: 24.0),

                      // Spectator Information Section
                      _buildSectionHeader(context, 'Spectator Information'),
                      _buildSpectatorInformationSection(),
                      const SizedBox(height: 24.0),

                      // Additional Contacts Section
                      _buildSectionHeader(context, 'Additional Contacts'),
                      _buildAdditionalContactsSection(),
                      const SizedBox(height: 32.0),

                      // Navigation Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          OutlinedButton(
                            onPressed: () {
                              // This navigation should go to the Game Timing Configuration Screen
                              context.go('/create-tournament/game-timing');
                            },
                            child: const Text('Back'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                // Create the tournament object with all the form data
                                // Use the existing tournament object from the BLoC state,
                                // and apply new values via copyWith.
                                final updatedTournament = state.tournament.copyWith(
                                  // Social media
                                  facebookUrl: _facebookUrlController.text.isNotEmpty ? _facebookUrlController.text.trim() : null,
                                  twitterUrl: _twitterUrlController.text.isNotEmpty ? _twitterUrlController.text.trim() : null,
                                  instagramUrl: _instagramUrlController.text.isNotEmpty ? _instagramUrlController.text.trim() : null,
                                  websiteUrl: _websiteUrlController.text.isNotEmpty ? _websiteUrlController.text.trim() : null,

                                  // Tournament details
                                  description: _descriptionController.text.isNotEmpty ? _descriptionController.text.trim() : null,

                                  // Registration details
                                  maxTeams: _maxTeamsController.text.isNotEmpty ? int.tryParse(_maxTeamsController.text) : null,

                                  // Tournament structure (gameDurationMinutes is now handled by gameTimingConfigurations)
                                  minRosterSize: _minRosterSizeController.text.isNotEmpty ? int.tryParse(_minRosterSizeController.text) : null,
                                  maxRosterSize: _maxRosterSizeController.text.isNotEmpty ? int.tryParse(_maxRosterSizeController.text) : null,

                                  // Awards and amenities
                                  awards: _awardsController.text.isNotEmpty ? _awardsController.text.trim() : null,
                                  hasConcessions: _hasConcessions,
                                  hasMerchandise: _hasMerchandise,
                                  hasMedical: _hasMedical,

                                  // Spectator information
                                  admissionFee: _admissionFeeController.text.isNotEmpty ? _admissionFeeController.text.trim() : null,
                                  parkingInfo: _parkingInfoController.text.isNotEmpty ? _parkingInfoController.text.trim() : null,
                                  spectatorInfo: _spectatorInfoController.text.isNotEmpty ? _spectatorInfoController.text.trim() : null,

                                  // Additional contacts
                                  secondaryContactName: _secondaryContactNameController.text.isNotEmpty ? _secondaryContactNameController.text.trim() : null,
                                  secondaryContactEmail: _secondaryContactEmailController.text.isNotEmpty ? _secondaryContactEmailController.text.trim() : null,
                                  secondaryContactPhone: _secondaryContactPhoneController.text.isNotEmpty ? _secondaryContactPhoneController.text.trim() : null,
                                  secondaryContactRole: _secondaryContactRoleController.text.isNotEmpty ? _secondaryContactRoleController.text.trim() : null,
                                );
                                // This event should now lead to a state for the "Review Screen"
                                context.read<CreateTournamentBloc>().add(AdditionalInfoStepCompleted(updatedTournament));
                              }
                            },
                            child: const Text('Next'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge,
      ),
    );
  }

  Widget _buildSocialMediaSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _facebookUrlController,
              decoration: const InputDecoration(
                labelText: 'Facebook URL',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.facebook),
              ),
              keyboardType: TextInputType.url,
              onChanged: (value) {
                // Trigger BLoC event on change to keep state updated
                context.read<CreateTournamentBloc>().add(FacebookUrlChanged(value));
              },
              validator: (value) {
                if (value != null && value.isNotEmpty && (Uri.tryParse(value)?.hasAbsolutePath != true)) {
                  return 'Enter a valid URL';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _twitterUrlController,
              decoration: const InputDecoration(
                labelText: 'Twitter URL',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.alternate_email), // Common icon for Twitter/X handle
              ),
              keyboardType: TextInputType.url,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(TwitterUrlChanged(value));
              },
              validator: (value) {
                if (value != null && value.isNotEmpty && (Uri.tryParse(value)?.hasAbsolutePath != true)) {
                  return 'Enter a valid URL';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _instagramUrlController,
              decoration: const InputDecoration(
                labelText: 'Instagram URL',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.camera_alt),
              ),
              keyboardType: TextInputType.url,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(InstagramUrlChanged(value));
              },
              validator: (value) {
                if (value != null && value.isNotEmpty && (Uri.tryParse(value)?.hasAbsolutePath != true)) {
                  return 'Enter a valid URL';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _websiteUrlController,
              decoration: const InputDecoration(
                labelText: 'Tournament Website URL',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.language),
              ),
              keyboardType: TextInputType.url,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(WebsiteUrlChanged(value));
              },
              validator: (value) {
                if (value != null && value.isNotEmpty && (Uri.tryParse(value)?.hasAbsolutePath != true)) {
                  return 'Enter a valid URL';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTournamentDetailsSection(CreateTournamentAdditionalInfoStep state) {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Tournament Description',
                border: OutlineInputBorder(),
                hintText: 'Provide a detailed description of your tournament',
              ),
              maxLines: 5,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(TournamentDescriptionChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            _buildLogoUploadSection(state),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationDetailsSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [


            TextFormField(
              controller: _maxTeamsController,
              decoration: const InputDecoration(
                labelText: 'Maximum Number of Teams',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.groups),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (value.isNotEmpty) {
                  try {
                    final maxTeams = int.parse(value);
                    context.read<CreateTournamentBloc>().add(MaxTeamsChanged(maxTeams));
                  } catch (e) {
                    // Handle parsing error
                  }
                }
              },
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final parsedValue = int.tryParse(value);
                  if (parsedValue == null || parsedValue < 1) {
                    return 'Enter a valid number (>=1)';
                  }
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTournamentStructureSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Min Roster Size
            TextFormField(
              controller: _minRosterSizeController,
              decoration: const InputDecoration(
                labelText: 'Minimum Roster Size',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person_outline),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (value.isNotEmpty) {
                  try {
                    final size = int.parse(value);
                    context.read<CreateTournamentBloc>().add(MinRosterSizeChanged(size));
                  } catch (e) {
                    // Handle parsing error
                  }
                }
              },
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final parsedValue = int.tryParse(value);
                  if (parsedValue == null || parsedValue < 1) {
                    return 'Enter a valid number (>=1)';
                  }
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 16.0),

            // Max Roster Size
            TextFormField(
              controller: _maxRosterSizeController,
              decoration: const InputDecoration(
                labelText: 'Maximum Roster Size',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.people),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (value.isNotEmpty) {
                  try {
                    final size = int.parse(value);
                    context.read<CreateTournamentBloc>().add(MaxRosterSizeChanged(size));
                  } catch (e) {
                    // Handle parsing error
                  }
                }
              },
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final parsedValue = int.tryParse(value);
                  if (parsedValue == null || parsedValue < 1) {
                    return 'Enter a valid number (>=1)';
                  }
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAwardsAndAmenitiesSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _awardsController,
              decoration: const InputDecoration(
                labelText: 'Awards Information',
                border: OutlineInputBorder(),
                hintText: 'Describe the awards for winners, runners-up, etc.',
                prefixIcon: Icon(Icons.emoji_events),
              ),
              maxLines: 3,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(AwardsChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            CheckboxListTile(
              title: const Text('Concessions Available'),
              value: _hasConcessions,
              onChanged: (bool? value) {
                setState(() {
                  _hasConcessions = value ?? false;
                });
                context.read<CreateTournamentBloc>().add(HasConcessionsChanged(_hasConcessions));
              },
              secondary: const Icon(Icons.fastfood),
            ),

            CheckboxListTile(
              title: const Text('Merchandise Sales'),
              value: _hasMerchandise,
              onChanged: (bool? value) {
                setState(() {
                  _hasMerchandise = value ?? false;
                });
                context.read<CreateTournamentBloc>().add(HasMerchandiseChanged(_hasMerchandise));
              },
              secondary: const Icon(Icons.shopping_bag),
            ),

            CheckboxListTile(
              title: const Text('Medical Services'),
              value: _hasMedical,
              onChanged: (bool? value) {
                setState(() {
                  _hasMedical = value ?? false;
                });
                context.read<CreateTournamentBloc>().add(HasMedicalChanged(_hasMedical));
              },
              secondary: const Icon(Icons.medical_services),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpectatorInformationSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _admissionFeeController,
              decoration: const InputDecoration(
                labelText: 'Admission Fee',
                border: OutlineInputBorder(),
                hintText: 'E.g., "Free", "\$5 per day", "\$10 for the weekend"',
                prefixIcon: Icon(Icons.confirmation_number),
              ),
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(AdmissionFeeChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _parkingInfoController,
              decoration: const InputDecoration(
                labelText: 'Parking Information',
                border: OutlineInputBorder(),
                hintText: 'Describe parking availability, fees, etc.',
                prefixIcon: Icon(Icons.local_parking),
              ),
              maxLines: 2,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(ParkingInfoChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _spectatorInfoController,
              decoration: const InputDecoration(
                labelText: 'Spectator Guidelines',
                border: OutlineInputBorder(),
                hintText: 'Any special instructions for spectators',
                prefixIcon: Icon(Icons.remove_red_eye),
              ),
              maxLines: 3,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(SpectatorInfoChanged(value));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalContactsSection() {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _secondaryContactNameController,
              decoration: const InputDecoration(
                labelText: 'Secondary Contact Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(SecondaryContactNameChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _secondaryContactEmailController,
              decoration: const InputDecoration(
                labelText: 'Secondary Contact Email',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(SecondaryContactEmailChanged(value));
              },
              validator: (value) {
                if (value != null && value.isNotEmpty && !value.contains('@')) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _secondaryContactPhoneController,
              decoration: const InputDecoration(
                labelText: 'Secondary Contact Phone',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(SecondaryContactPhoneChanged(value));
              },
            ),
            const SizedBox(height: 16.0),

            TextFormField(
              controller: _secondaryContactRoleController,
              decoration: const InputDecoration(
                labelText: 'Secondary Contact Role',
                border: OutlineInputBorder(),
                hintText: 'E.g., "Assistant Director", "Field Coordinator"',
                prefixIcon: Icon(Icons.work),
              ),
              onChanged: (value) {
                context.read<CreateTournamentBloc>().add(SecondaryContactRoleChanged(value));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoUploadSection(CreateTournamentAdditionalInfoStep state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display current logo or placeholder
        if (state.tournament.logoUrl != null && state.tournament.logoUrl!.isNotEmpty) ...[
          Container(
            height: 100,
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                state.tournament.logoUrl!,
                height: 100,
                width: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 100,
                    width: 100,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.error, size: 50, color: Colors.red),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    height: 100,
                    width: 100,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(child: CircularProgressIndicator()),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
        ] else ...[
          Container(
            height: 100,
            width: 100,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[400]!),
            ),
            child: const Icon(Icons.image, size: 50, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text('No Logo', style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 8),
        ],

        // Upload/Change Logo Button
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _isUploadingLogo ? null : _uploadLogo,
              icon: _isUploadingLogo
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.upload_file),
              label: Text(state.tournament.logoUrl != null && state.tournament.logoUrl!.isNotEmpty
                  ? 'Change Logo'
                  : 'Upload Logo'),
            ),
            if (state.tournament.logoUrl != null && state.tournament.logoUrl!.isNotEmpty) ...[
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: _isUploadingLogo ? null : _removeLogo,
                icon: const Icon(Icons.delete),
                label: const Text('Remove'),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Future<void> _uploadLogo() async {
    try {
      setState(() {
        _isUploadingLogo = true;
      });

      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileExt = file.path.split('.').last;

        // Get current tournament state
        final currentState = context.read<CreateTournamentBloc>().state;
        if (currentState is CreateTournamentAdditionalInfoStep) {
          final tournament = currentState.tournament;

          // Define the Supabase Storage path
          final filePath = 'public/tournament-logos/${tournament.id ?? UniqueKey().toString()}/${DateTime.now().millisecondsSinceEpoch}.$fileExt';

          // Upload to Supabase Storage
          final supa = Supabase.instance.client;

          // First, ensure the bucket exists
          try {
            await supa.storage.from('tournament-logos').upload(filePath, file);
          } catch (e) {
            // If bucket doesn't exist, create it first
            if (e.toString().contains('Bucket not found')) {
              await supa.storage.createBucket('tournament-logos', BucketOptions(public: true));
              await supa.storage.from('tournament-logos').upload(filePath, file);
            } else {
              rethrow;
            }
          }

          final imageUrl = supa.storage.from('tournament-logos').getPublicUrl(filePath);

          // Dispatch event to BLoC
          if (mounted) {
            context.read<CreateTournamentBloc>().add(TournamentLogoUrlChanged(imageUrl));
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Logo upload failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingLogo = false;
        });
      }
    }
  }

  void _removeLogo() {
    context.read<CreateTournamentBloc>().add(const TournamentLogoUrlChanged(null));
  }
}