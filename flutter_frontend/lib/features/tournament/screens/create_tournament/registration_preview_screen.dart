import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../data/models/tournament.dart';
import '../../../../data/models/division.dart';

class RegistrationPreviewScreen extends StatelessWidget {
  final Tournament tournament;

  const RegistrationPreviewScreen({
    super.key,
    required this.tournament,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Registration Page Preview'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 24),
            _buildDescription(context),
            const SizedBox(height: 24),
            _buildDivisionsSection(context),
            const SizedBox(height: 24),
            _buildFeeInformationSection(context),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(context),
            const SizedBox(height: 24),
            _buildContactSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tournament Logo (if available) - TODO: Implement in RWF.N14
            // if (tournament.logoUrl != null && tournament.logoUrl!.isNotEmpty) ...[
            //   Center(
            //     child: Image.network(
            //       tournament.logoUrl!,
            //       height: 100,
            //       errorBuilder: (context, error, stackTrace) {
            //         return Container(
            //           height: 100,
            //           width: 100,
            //           decoration: BoxDecoration(
            //             color: Colors.grey[300],
            //             borderRadius: BorderRadius.circular(8),
            //           ),
            //           child: const Icon(Icons.image_not_supported, size: 40),
            //         );
            //       },
            //     ),
            //   ),
            //   const SizedBox(height: 16),
            // ],
            
            // Tournament Name
            Text(
              tournament.name,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            
            // Tournament Dates
            Row(
              children: [
                Icon(Icons.calendar_today, 
                     size: 16, 
                     color: Theme.of(context).colorScheme.onSurfaceVariant),
                const SizedBox(width: 8),
                Text(
                  'Dates: ${_formatDate(tournament.startDate)} - ${_formatDate(tournament.endDate)}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Location
            Row(
              children: [
                Icon(Icons.location_on, 
                     size: 16, 
                     color: Theme.of(context).colorScheme.onSurfaceVariant),
                const SizedBox(width: 8),
                Text(
                  'Location: ${tournament.city ?? 'N/A'}, ${tournament.state ?? 'N/A'}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            
            // Tournament Format
            if (tournament.tournamentFormat != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.sports_soccer, 
                       size: 16, 
                       color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(
                    'Format: ${tournament.tournamentFormat}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    if (tournament.description == null || tournament.description!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About This Tournament',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              tournament.description!,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivisionsSection(BuildContext context) {
    if (tournament.divisions == null || tournament.divisions!.isEmpty) {
      return Card(
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Offered Divisions',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'No divisions configured yet.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Offered Divisions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...tournament.divisions!.map((division) => _buildDivisionCard(context, division)),
          ],
        ),
      ),
    );
  }

  Widget _buildDivisionCard(BuildContext context, Division division) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    division.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '\$${division.registrationFee.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildInfoChip(context, 'Age: ${division.ageGroup}'),
                _buildInfoChip(context, 'Gender: ${division.gender}'),
                _buildInfoChip(context, 'Level: ${division.competitiveLevel}'),
                _buildInfoChip(context, 'Format: ${division.playFormat}'),
              ],
            ),
            if (division.notes != null && division.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${division.notes}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSecondaryContainer,
        ),
      ),
    );
  }

  Widget _buildFeeInformationSection(BuildContext context) {
    final hasEarlyBird = tournament.earlyBirdDiscount != null && 
                         tournament.earlyBirdDiscount! > 0 &&
                         tournament.earlyBirdDeadline != null;
    final hasLateFee = tournament.lateFeeAmount != null && 
                       tournament.lateFeeAmount! > 0 &&
                       tournament.lateRegistrationStartDate != null;

    if (!hasEarlyBird && !hasLateFee) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fee Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            if (hasEarlyBird) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.savings, color: Colors.green[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Early Bird Discount: \$${tournament.earlyBirdDiscount!.toStringAsFixed(2)} until ${_formatDate(tournament.earlyBirdDeadline!)}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            if (hasEarlyBird && hasLateFee) const SizedBox(height: 8),
            
            if (hasLateFee) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Late Fee: \$${tournament.lateFeeAmount!.toStringAsFixed(2)} applies after ${_formatDate(tournament.lateRegistrationStartDate!)}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection(BuildContext context) {
    final hasRules = tournament.rules != null && tournament.rules!.isNotEmpty;
    final hasRefundPolicy = tournament.refundPolicy != null && tournament.refundPolicy!.isNotEmpty;

    if (!hasRules && !hasRefundPolicy) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tournament Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            if (hasRules) ...[
              Text(
                'Rules & Regulations',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                tournament.rules!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              if (hasRefundPolicy) const SizedBox(height: 16),
            ],

            if (hasRefundPolicy) ...[
              Text(
                'Refund Policy',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                tournament.refundPolicy!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            if (tournament.directorName != null && tournament.directorName!.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.person,
                       size: 16,
                       color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(
                    'Tournament Director: ${tournament.directorName}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            if (tournament.directorEmail != null && tournament.directorEmail!.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.email,
                       size: 16,
                       color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(
                    'Email: ${tournament.directorEmail}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // Registration deadline
            if (tournament.registrationDeadline != null) ...[
              Row(
                children: [
                  Icon(Icons.schedule,
                       size: 16,
                       color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Text(
                    'Registration Deadline: ${_formatDate(tournament.registrationDeadline!)}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat.yMMMd().format(date.toLocal());
  }
}
