name: soccer_frontend
description: Flutter frontend for Soccer Tournament Platform.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2 # Can leave as is or check for latest minor
  flutter_bloc: ^8.1.3 # Can leave as is or check for latest minor/patch
  equatable: ^2.0.5 # Generally stable
  supabase_flutter: ^2.5.0 # Upgrade to latest stable
  go_router: ^12.1.1 # Can leave as is or check for latest minor/patch
  logging: ^1.2.0 # Generally stable
  json_annotation: ^4.8.1 # Generally stable
  flutter_dotenv: ^5.1.0
  provider: ^6.1.1
  intl: ^0.18.1
  flutter_secure_storage: ^9.0.0
  flutter_web_plugins:
    sdk: flutter
  uni_links: ^0.5.1 # This package is discontinued and replaced by app_links. Consider migrating.
  supabase_auth_ui: ^0.5.5 # Use version suggested by solver
  jwt_decoder: ^2.0.1 # Generally stable
  http: ^1.2.0 # For making HTTP requests to the backend API
  file_picker: ^6.2.1 # For tournament logo upload

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  mocktail: ^1.0.1
  bloc_test: ^9.1.5

flutter:
  uses-material-design: true
  assets:
    # Try listing the root directory as well
    - ./ 
    - .env 
    # You might also need to list assets/ if you have other assets there
    # - assets/
